import { consume } from '@lit/context';
import { SignalWatcher } from '@lit-labs/signals';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, state } from 'lit/decorators.js';
import type {
	GetInactiveOverviewResponse,
	GetOverviewBranch,
	OverviewFilterType,
	State,
} from '../../../../home/<USER>';
import { SetOverviewFilter } from '../../../../home/<USER>';
import { stateContext } from '../../../home/<USER>';
import { ipcContext } from '../../../shared/contexts/ipc';
import type { HostIpc } from '../../../shared/ipc';
import { linkStyles } from '../../shared/components/vscode.css';
import type { InactiveOverviewState } from './overviewState';
import { inactiveOverviewStateContext } from './overviewState';
import '../../../shared/components/skeleton-loader';
import './branch-threshold-filter';

export const overviewTagName = 'gl-overview';

@customElement(overviewTagName)
export class GlOverview extends SignalWatcher(LitElement) {
	static override styles = [
		linkStyles,
		css`
			:host {
				display: block;
				margin-bottom: 2.4rem;
				color: var(--vscode-foreground);
			}
		`,
	];

	@consume<State>({ context: stateContext, subscribe: true })
	@state()
	private _homeState!: State;

	@consume({ context: inactiveOverviewStateContext })
	private _inactiveOverviewState!: InactiveOverviewState;

	override connectedCallback(): void {
		super.connectedCallback?.();

		if (this._homeState.repositories.openCount > 0) {
			this._inactiveOverviewState.run();
		}
	}

	override render(): unknown {
		if (this._homeState.discovering) {
			return this.renderLoader();
		}

		if (this._homeState.repositories.openCount === 0) {
			return nothing;
		}

		return this._inactiveOverviewState.render({
			pending: () => this.renderPending(),
			complete: summary => this.renderComplete(summary),
			error: () => html`<span>Error</span>`,
		});
	}

	private renderLoader() {
		return html`
			<gl-section>
				<skeleton-loader slot="heading" lines="1"></skeleton-loader>
				<skeleton-loader lines="3"></skeleton-loader>
			</gl-section>
		`;
	}

	private renderPending() {
		if (this._inactiveOverviewState.state == null) {
			return this.renderLoader();
		}
		return this.renderComplete(this._inactiveOverviewState.state, true);
	}

	@consume({ context: ipcContext })
	private readonly _ipc!: HostIpc;

	private readonly onChangeFilter = (e: CustomEvent<{ threshold: OverviewFilterType }>) => {
		if (!this._inactiveOverviewState.filter.stale || !this._inactiveOverviewState.filter.recent) {
			return;
		}

		const filterValue = e.detail.threshold;

		if (filterValue === 'favorites') {
			// Set content type to favorites
			this._ipc.sendCommand(SetOverviewFilter, {
				stale: this._inactiveOverviewState.filter.stale,
				recent: { ...this._inactiveOverviewState.filter.recent, contentType: 'favorites' },
			});
		} else if (filterValue === 'stale') {
			// Show stale branches
			this._ipc.sendCommand(SetOverviewFilter, {
				stale: { ...this._inactiveOverviewState.filter.stale, show: true },
				recent: { ...this._inactiveOverviewState.filter.recent, contentType: 'recent' },
			});
		} else {
			// Handle time-based filters (OneDay, OneWeek, OneMonth)
			this._ipc.sendCommand(SetOverviewFilter, {
				stale: this._inactiveOverviewState.filter.stale,
				recent: {
					...this._inactiveOverviewState.filter.recent,
					threshold: filterValue,
					contentType: 'recent',
				},
			});
		}
	};

	private getEmptyMessage(filter: OverviewFilterType): string {
		switch (filter) {
			case 'favorites':
				return 'No favorite branches';
			case 'stale':
				return 'No stale branches';
			default:
				return 'No recent branches';
		}
	}

	private renderComplete(overview: GetInactiveOverviewResponse, isFetching = false) {
		if (overview == null) return nothing;
		const { repository } = overview;
		const contentType = this._inactiveOverviewState.filter.recent?.contentType ?? 'recent';
		const showStale = this._inactiveOverviewState.filter.stale?.show === true;

		let branches: GetOverviewBranch[];
		let currentFilter: OverviewFilterType;

		if (showStale && overview.stale) {
			branches = overview.stale;
			currentFilter = 'stale';
		} else if (contentType === 'favorites') {
			branches = overview.favorites ?? [];
			currentFilter = 'favorites';
		} else {
			branches = overview.recent;
			currentFilter = this._inactiveOverviewState.filter.recent?.threshold ?? 'OneWeek';
		}

		return html`
			<gl-branch-section
				label="Branches"
				.isFetching=${isFetching}
				.repo=${repository.path}
				.branches=${branches}
				.emptyMessage=${this.getEmptyMessage(currentFilter)}
			>
				<gl-branch-threshold-filter
					slot="heading-actions"
					@gl-change=${this.onChangeFilter}
					.options=${[
						{ value: 'favorites', label: 'Favorites' },
						{ value: 'OneDay', label: '1 day' },
						{ value: 'OneWeek', label: '1 week' },
						{ value: 'OneMonth', label: '1 month' },
						{ value: 'stale', label: 'Stale' },
					]}
					.disabled=${isFetching}
					.value=${currentFilter}
				></gl-branch-threshold-filter>
			</gl-branch-section>
		`;
	}
}

declare global {
	interface HTMLElementTagNameMap {
		[overviewTagName]: GlOverview;
	}
}
