import { css, html } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { repeat } from 'lit/directives/repeat.js';
import type { OverviewContentType } from '../../../../home/<USER>';
import { GlElement } from '../../../shared/components/element';
import '../../../shared/components/checkbox/checkbox';
import '../../../shared/components/code-icon';

const selectStyles = css`
	.select {
		appearance: none;
		background: var(--vscode-dropdown-background);
		border: 1px solid var(--vscode-dropdown-border);
		border-radius: 2px;
		color: var(--vscode-dropdown-foreground);
		cursor: pointer;
		font-family: inherit;
		font-size: inherit;
		line-height: 1.4;
		padding: 0.25rem 2rem 0.25rem 0.75rem;
		text-align: left;
		white-space: nowrap;
		background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='16' height='16' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg' fill='%23cccccc'%3E%3Cpath d='m4 6 4 4 4-4'/%3E%3C/svg%3E");
		background-position: calc(100% - 0.75rem) center;
		background-repeat: no-repeat;
		background-size: 1rem;
	}

	.select:focus {
		border-color: var(--vscode-focusBorder);
		outline: 1px solid var(--vscode-focusBorder);
		outline-offset: -1px;
	}

	.select:disabled {
		cursor: not-allowed;
		opacity: 0.4;
	}
`;

export abstract class GlObjectSelect<T, L = T[keyof T], V = T[keyof T]> extends GlElement {
	static override readonly styles = [selectStyles];

	@property({ type: Boolean }) disabled: boolean = false;
	@property({ type: String }) value?: V;
	@property({ type: Array }) options?: T[];

	protected abstract getValue(option: T): V;
	protected abstract getLabel(option: T): L;
	protected abstract onChange?(e: InputEvent): unknown;

	override render(): unknown {
		if (!this.options) {
			return;
		}
		return html`
			<select .disabled=${this.disabled} class="select" @change=${(e: InputEvent) => this.onChange?.(e)}>
				${repeat(this.options, item => {
					const value = this.getValue(item);
					const label = this.getLabel(item);
					return html`<option .value="${value}" ?selected=${this.value === value}>${label}</option>`;
				})}
			</select>
		`;
	}
}

@customElement('gl-branch-content-filter')
export class GlBranchContentFilter extends GlObjectSelect<{
	value: OverviewContentType;
	label: string;
}> {
	protected getValue(option: {
		value: OverviewContentType;
	}): OverviewContentType {
		return option.value;
	}
	protected getLabel(option: { label: string }): string {
		return option.label;
	}
	protected onChange(e: InputEvent): void {
		const event = new CustomEvent('gl-change', {
			detail: {
				contentType: (e.target as HTMLSelectElement).value as OverviewContentType,
			},
		});
		this.dispatchEvent(event);
	}
}

declare global {
	interface HTMLElementTagNameMap {
		'gl-branch-content-filter': GlBranchContentFilter;
	}
}
